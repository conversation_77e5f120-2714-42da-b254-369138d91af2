#!/usr/bin/env python3
"""
高级Logo矢量化工具
使用更专业的算法生成高质量SVG
"""

import cv2
import numpy as np
from PIL import Image
import os
import sys
from skimage import measure, morphology, filters
from skimage.segmentation import felzenszwalb, slic
from skimage.color import rgb2lab, lab2rgb
import matplotlib.pyplot as plt

def enhance_image_quality(image_path):
    """增强图像质量，为矢量化做准备"""
    print(f"正在增强图像质量: {image_path}")
    
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"错误: 无法读取图像 {image_path}")
        return None
    
    # 转换为RGB
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    # 获取原始尺寸
    height, width = img_rgb.shape[:2]
    
    # 如果图像太小，先放大
    if max(height, width) < 400:
        scale = 400 / max(height, width)
        new_width = int(width * scale)
        new_height = int(height * scale)
        img_rgb = cv2.resize(img_rgb, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
    
    # 如果图像太大，适当缩小
    elif max(height, width) > 1200:
        scale = 1200 / max(height, width)
        new_width = int(width * scale)
        new_height = int(height * scale)
        img_rgb = cv2.resize(img_rgb, (new_width, new_height), interpolation=cv2.INTER_AREA)
    
    # 去噪声
    denoised = cv2.fastNlMeansDenoisingColored(img_rgb, None, 10, 10, 7, 21)
    
    # 增强对比度
    lab = cv2.cvtColor(denoised, cv2.COLOR_RGB2LAB)
    l, a, b = cv2.split(lab)
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    l = clahe.apply(l)
    enhanced = cv2.merge([l, a, b])
    enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2RGB)
    
    return enhanced

def create_high_quality_svg_with_segmentation(image_path, output_path):
    """使用图像分割创建高质量SVG"""
    print(f"正在创建高质量分割SVG: {output_path}")
    
    # 增强图像
    enhanced_img = enhance_image_quality(image_path)
    if enhanced_img is None:
        return
    
    height, width = enhanced_img.shape[:2]
    
    # 使用SLIC超像素分割
    segments = slic(enhanced_img, n_segments=50, compactness=10, sigma=1, start_label=1)
    
    # 获取每个分割区域的平均颜色
    segment_colors = {}
    for segment_id in np.unique(segments):
        mask = segments == segment_id
        if np.sum(mask) > 20:  # 忽略太小的区域
            avg_color = np.mean(enhanced_img[mask], axis=0)
            segment_colors[segment_id] = avg_color.astype(int)
    
    # 创建SVG
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="{width}" height="{height}" viewBox="0 0 {width} {height}">
  <defs>
    <style>
      .segment-path {{ fill-rule: evenodd; }}
    </style>
  </defs>
'''
    
    # 为每个分割区域创建路径
    for segment_id, color in segment_colors.items():
        # 创建该分割区域的掩码
        mask = (segments == segment_id).astype(np.uint8) * 255
        
        # 形态学操作平滑边界
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 转换颜色为十六进制
        hex_color = f"#{color[0]:02x}{color[1]:02x}{color[2]:02x}"
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 50:  # 过滤小区域
                # 使用Douglas-Peucker算法简化轮廓
                epsilon = 0.002 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                if len(approx) >= 3:
                    # 创建平滑的路径
                    path_data = create_smooth_path(approx)
                    svg_content += f'  <path class="segment-path" fill="{hex_color}" d="{path_data}"/>\n'
    
    svg_content += '</svg>'
    
    # 保存SVG文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(svg_content)
    
    print(f"高质量分割SVG已保存: {output_path}")

def create_smooth_path(points):
    """创建平滑的SVG路径"""
    if len(points) < 3:
        return ""
    
    path_data = f"M {points[0][0][0]} {points[0][0][1]}"
    
    # 使用二次贝塞尔曲线创建平滑路径
    for i in range(1, len(points)):
        curr_point = points[i][0]
        if i < len(points) - 1:
            next_point = points[(i + 1) % len(points)][0]
            # 计算控制点
            control_x = (curr_point[0] + next_point[0]) / 2
            control_y = (curr_point[1] + next_point[1]) / 2
            path_data += f" Q {curr_point[0]} {curr_point[1]} {control_x} {control_y}"
        else:
            path_data += f" L {curr_point[0]} {curr_point[1]}"
    
    path_data += " Z"
    return path_data

def create_clean_vector_svg(image_path, output_path):
    """创建清晰的矢量SVG"""
    print(f"正在创建清晰矢量SVG: {output_path}")
    
    # 增强图像
    enhanced_img = enhance_image_quality(image_path)
    if enhanced_img is None:
        return
    
    height, width = enhanced_img.shape[:2]
    
    # 转换为灰度
    gray = cv2.cvtColor(enhanced_img, cv2.COLOR_RGB2GRAY)
    
    # 使用自适应阈值
    binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, 8)
    
    # 形态学操作清理图像
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=2)
    cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel, iterations=1)
    
    # 查找轮廓
    contours, hierarchy = cv2.findContours(cleaned, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    
    # 创建SVG
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="{width}" height="{height}" viewBox="0 0 {width} {height}">
  <defs>
    <style>
      .vector-path {{
        fill: #000000;
        fill-rule: evenodd;
      }}
    </style>
  </defs>
  <rect width="{width}" height="{height}" fill="white"/>
'''
    
    # 处理轮廓
    valid_contours = []
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        if area > 100:  # 过滤小轮廓
            valid_contours.append((contour, area))
    
    # 按面积排序
    valid_contours.sort(key=lambda x: x[1], reverse=True)
    
    # 处理主要轮廓
    for contour, area in valid_contours[:15]:  # 限制轮廓数量
        # 精确的轮廓近似
        epsilon = 0.001 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        if len(approx) >= 3:
            path_data = create_smooth_path(approx)
            svg_content += f'  <path class="vector-path" d="{path_data}"/>\n'
    
    svg_content += '</svg>'
    
    # 保存SVG文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(svg_content)
    
    print(f"清晰矢量SVG已保存: {output_path}")

def main():
    """主函数"""
    print("开始高级Logo矢量化处理...")
    
    # 检查输入文件
    logo_files = ['logo.jpg', 'logo1.jpg']
    
    for logo_file in logo_files:
        if not os.path.exists(logo_file):
            print(f"警告: 文件 {logo_file} 不存在，跳过处理")
            continue
        
        print(f"\n处理文件: {logo_file}")
        
        # 生成输出文件名
        base_name = os.path.splitext(logo_file)[0]
        segmented_svg = f"{base_name}_segmented.svg"
        vector_svg = f"{base_name}_vector.svg"
        
        # 创建高质量分割SVG
        create_high_quality_svg_with_segmentation(logo_file, segmented_svg)
        
        # 创建清晰矢量SVG
        create_clean_vector_svg(logo_file, vector_svg)
        
        print(f"完成处理: {logo_file}")
    
    print("\n高级Logo矢量化处理完成！")
    print("生成的文件:")
    for logo_file in logo_files:
        if os.path.exists(logo_file):
            base_name = os.path.splitext(logo_file)[0]
            print(f"  - {base_name}_segmented.svg (高质量分割版本)")
            print(f"  - {base_name}_vector.svg (清晰矢量版本)")

if __name__ == "__main__":
    main()
